"""
并行处理模块 - 用于代理关系分析工具中的多线程数据处理
提供高效的并行计算功能，同时保持与原接口的兼容性
"""

import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from functools import partial
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('parallel_processor')

class ParallelProcessor:
    """并行处理器类，用于并行化数据处理任务"""
    
    def __init__(self, max_workers=None, chunk_size=None, use_process_pool=False, 
                 min_data_size=1000, min_groups_size=50, enable_parallel=True):
        """
        初始化并行处理器
        
        参数:
            max_workers (int): 最大工作线程数，默认为CPU核心数 * 2
            chunk_size (int): 数据分块大小，默认为None（自动计算）
            use_process_pool (bool): 是否使用进程池代替线程池，对CPU密集型任务更有效
            min_data_size (int): 启用并行处理的最小数据量，小于此值时使用单线程
            min_groups_size (int): 启用并行处理的最小分组数，小于此值时使用单线程
            enable_parallel (bool): 是否启用并行处理，设为False时强制使用单线程
        """
        # 如果max_workers为None，则使用CPU核心数 * 2
        import multiprocessing
        self.max_workers = max_workers or min(8, multiprocessing.cpu_count() * 2)
        self.chunk_size = chunk_size
        self.use_process_pool = use_process_pool
        self.min_data_size = min_data_size
        self.min_groups_size = min_groups_size
        self.enable_parallel = enable_parallel
        
        # 选择执行器类型
        self.executor_class = ProcessPoolExecutor if self.use_process_pool else ThreadPoolExecutor
        
        # 减少初始化日志，只在调试时输出
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(f"初始化并行处理器，最大工作线程数: {self.max_workers}，" 
                        f"使用{'进程池' if use_process_pool else '线程池'}，"
                        f"并行处理{'已启用' if enable_parallel else '已禁用'}")
    
    def find_shared_relationships_parallel(self, df, shared_field):
        """
        并行查找共享关系
        
        参数:
            df (DataFrame): 包含数据的DataFrame
            shared_field (str): 共享字段名称（例如'device_id'或'ip'）
            
        返回:
            list: 共享关系列表
        """
        if df.empty:
            logger.warning(f"传入的DataFrame为空，无法查找{shared_field}共享关系")
            return []
        
        start_time = time.time()
        
        # 剔除共享字段为空的记录
        valid_df = df.dropna(subset=[shared_field])
        if valid_df.empty:
            logger.warning(f"过滤后的DataFrame为空，无法查找{shared_field}共享关系")
            return []
        
        # 确保agent_level列存在
        if 'agent_level' not in valid_df.columns:
            valid_df['agent_level'] = '未知级别'
        
        # 按共享字段分组
        groups = valid_df.groupby(shared_field)
        
        # 过滤掉只有一条记录的组
        filtered_groups = [(name, group) for name, group in groups if len(group) >= 2]
        
        if not filtered_groups:
            logger.info(f"没有找到{shared_field}的共享关系")
            return []
        
        # 确定是否需要并行处理
        n_groups = len(filtered_groups)
        n_records = sum(len(group) for _, group in filtered_groups)
        
        # 如果数据量小于阈值，使用单线程处理
        if not self.enable_parallel or n_groups < self.min_groups_size or n_records < self.min_data_size:
            logger.debug(f"数据量较小({n_groups}组，{n_records}条记录)，使用单线程处理{shared_field}共享关系")
            result = self._process_groups_chunk(filtered_groups, shared_field)

            # 🔧 修复：在单线程模式下也进行去重
            result = self._deduplicate_relationships(result)

            elapsed_time = time.time() - start_time
            logger.info(f"完成{shared_field}共享关系查找，找到{len(result)}个关系，耗时{elapsed_time:.2f}秒")

            # 对结果进行排序，确保一致性
            result.sort(key=lambda x: (str(x['user_a_mid']), str(x['user_b_mid'])))
            return result
        
        # 确定分块大小和线程数
        actual_workers = min(self.max_workers, max(1, n_groups // 10 + 1))
        
        # 将分组列表分块
        chunks = self._split_list(filtered_groups, actual_workers)
        
        logger.debug(f"开始并行处理{shared_field}共享关系，共{n_groups}个分组，使用{actual_workers}个线程")
        
        # 创建线程池并分配任务
        with self.executor_class(max_workers=actual_workers) as executor:
            process_func = partial(self._process_groups_chunk, shared_field=shared_field)
            results = list(executor.map(process_func, chunks))
        
        # 合并结果
        final_result = [item for sublist in results for item in sublist]

        # 🔧 修复：在并行模式下进行全局去重
        final_result = self._deduplicate_relationships(final_result)

        elapsed_time = time.time() - start_time
        logger.info(f"完成{shared_field}共享关系查找，找到{len(final_result)}个关系，耗时{elapsed_time:.2f}秒")

        # 对结果进行排序，确保一致性
        final_result.sort(key=lambda x: (str(x['user_a_mid']), str(x['user_b_mid'])))
        return final_result
    
    def find_both_shared_parallel(self, device_shared, ip_shared):
        """
        并行查找同时共享设备和IP的关系
        
        参数:
            device_shared (list): 共享设备关系列表
            ip_shared (list): 共享IP关系列表
            
        返回:
            list: 同时共享设备和IP的关系列表
        """
        if not device_shared or not ip_shared:
            logger.warning("共享设备或共享IP的列表为空，无法查找同时共享的关系")
            return []
        
        start_time = time.time()
        
        # 创建设备共享用户对字典
        device_pairs = {}
        for item in device_shared:
            user_pair = tuple(sorted([item['user_a_mid'], item['user_b_mid']]))
            if user_pair not in device_pairs:
                device_pairs[user_pair] = []
            device_pairs[user_pair].append(item)
        
        # 判断是否需要并行处理
        if not self.enable_parallel or len(ip_shared) < self.min_data_size:
            logger.info(f"数据量较小({len(ip_shared)}条IP共享记录)，使用单线程处理两者共享关系")
            result = self._find_both_shared_chunk(ip_shared, device_pairs)
            
            elapsed_time = time.time() - start_time
            logger.info(f"完成两者都共享关系查找，共找到{len(result)}个关系，耗时{elapsed_time:.2f}秒")
            
            # 对结果进行排序，确保一致性
            result.sort(key=lambda x: (str(x['user_a_mid']), str(x['user_b_mid'])))
            return result
        
        # 将IP共享列表分块
        actual_workers = min(self.max_workers, max(1, len(ip_shared) // 1000 + 1))
        chunks = self._split_list(ip_shared, actual_workers)
        
        # 创建处理函数
        process_func = partial(self._find_both_shared_chunk, device_pairs=device_pairs)
        
        # 创建线程池并分配任务
        with self.executor_class(max_workers=actual_workers) as executor:
            results = list(executor.map(process_func, chunks))
        
        # 合并结果
        final_result = [item for sublist in results for item in sublist]
        
        elapsed_time = time.time() - start_time
        logger.info(f"完成两者都共享关系查找，共找到{len(final_result)}个关系，耗时{elapsed_time:.2f}秒")
        
        # 对结果进行排序，确保一致性
        final_result.sort(key=lambda x: (str(x['user_a_mid']), str(x['user_b_mid'])))
        return final_result
    
    def filter_data_parallel(self, data, search_term, filter_bd, filter_unassigned_bd, filter_ourbit_internal, filter_mode='all'):
        """
        并行过滤数据
        
        参数:
            data (list): 要过滤的数据列表
            search_term (str): 搜索关键词
            filter_bd (str): BD团队过滤条件
            filter_unassigned_bd (bool): 是否过滤未分配BD的数据
            filter_ourbit_internal (bool): 是否过滤Ourbit_Internal的数据
            filter_mode (str): 筛选模式 - 'all'(所有), 'same_bd'(同一BD), 'diff_bd'(不同BD)
            
        返回:
            list: 过滤后的数据列表
        """
        if not data:
            return []
        
        start_time = time.time()
        
        # 判断是否需要并行处理
        if not self.enable_parallel or len(data) < self.min_data_size:
            logger.info(f"数据量较小({len(data)}条记录)，使用单线程过滤数据")
            result = self._filter_data_chunk(data, search_term, filter_bd, filter_unassigned_bd, filter_ourbit_internal, filter_mode)
            
            elapsed_time = time.time() - start_time
            logger.info(f"完成数据过滤，输入数据{len(data)}条，过滤后{len(result)}条，耗时{elapsed_time:.2f}秒")
            
            # 对结果进行排序，确保一致性
            result.sort(key=lambda x: (str(x['user_a_mid']), str(x['user_b_mid'])))
            return result
        
        # 将数据列表分块
        actual_workers = min(self.max_workers, max(1, len(data) // 1000 + 1))
        chunks = self._split_list(data, actual_workers)
        
        # 创建处理函数
        process_func = partial(
            self._filter_data_chunk,
            search_term=search_term,
            filter_bd=filter_bd,
            filter_unassigned_bd=filter_unassigned_bd,
            filter_ourbit_internal=filter_ourbit_internal,
            filter_mode=filter_mode
        )
        
        # 创建线程池并分配任务
        with self.executor_class(max_workers=actual_workers) as executor:
            results = list(executor.map(process_func, chunks))
        
        # 合并结果
        final_result = [item for sublist in results for item in sublist]
        
        elapsed_time = time.time() - start_time
        logger.info(f"完成数据过滤，输入数据{len(data)}条，过滤后{len(final_result)}条，耗时{elapsed_time:.2f}秒")
        
        # 对结果进行排序，确保一致性
        final_result.sort(key=lambda x: (str(x['user_a_mid']), str(x['user_b_mid'])))
        return final_result
    
    def analyze_bd_statistics_parallel(self, device_shared, ip_shared, both_shared):
        """
        并行分析BD团队统计数据
        
        参数:
            device_shared (list): 共享设备关系列表
            ip_shared (list): 共享IP关系列表
            both_shared (list): 同时共享设备和IP的关系列表
            
        返回:
            dict: BD团队统计数据
        """
        start_time = time.time()
        
        # 创建任务列表
        tasks = [
            ('device', device_shared),
            ('ip', ip_shared),
            ('both', both_shared)
        ]
        
        # 判断是否需要并行处理
        total_data_size = len(device_shared) + len(ip_shared) + len(both_shared)
        if not self.enable_parallel or total_data_size < self.min_data_size:
            logger.info(f"数据量较小(总计{total_data_size}条记录)，使用单线程分析BD统计")
            results = [self._analyze_bd_statistics_for_type(*task) for task in tasks]
        else:
            # 创建线程池并分配任务
            with self.executor_class(max_workers=min(3, self.max_workers)) as executor:
                results = list(executor.map(
                    lambda task: self._analyze_bd_statistics_for_type(*task),
                    tasks
                ))
        
        # 合并结果
        final_result = {
            'device': results[0],
            'ip': results[1],
            'both': results[2]
        }
        
        elapsed_time = time.time() - start_time
        logger.info(f"完成BD团队统计数据分析，耗时{elapsed_time:.2f}秒")
        
        return final_result
    
    # 辅助方法
    def _split_list(self, data_list, n_chunks):
        """将列表分成n_chunks个块"""
        if not data_list:
            return []
        
        chunk_size = max(1, len(data_list) // n_chunks)
        return [data_list[i:i + chunk_size] for i in range(0, len(data_list), chunk_size)]
    
    def _process_groups_chunk(self, groups_chunk, shared_field):
        """处理分组数据块，查找共享关系"""
        result = []

        for name, group in groups_chunk:
            # 获取当前共享资源下的所有用户ID - 修复：使用digital_id而不是member_id
            all_user_ids = group['digital_id'].unique()

            # 如果只有一个用户ID，跳过
            if len(all_user_ids) < 2:
                continue

            # 🔧 修复：使用全局去重集合，避免重复处理用户对
            processed_pairs = set()

            # 获取每个用户的最新登录记录 - 修复：使用digital_id作为键
            latest_records = {}
            for _, record in group.iterrows():
                user_id = record['digital_id']  # 修复：使用digital_id而不是member_id
                created_time = record['created_time']

                if user_id not in latest_records or created_time > latest_records[user_id]['created_time']:
                    latest_records[user_id] = record.to_dict()

            # 统计每对用户的匹配次数（基于原始数据）
            user_pairs_count = {}
            for i in range(len(all_user_ids)):
                for j in range(i+1, len(all_user_ids)):
                    user_a_id = all_user_ids[i]
                    user_b_id = all_user_ids[j]

                    # 确保用户ID有序，便于统计
                    if user_a_id > user_b_id:
                        user_a_id, user_b_id = user_b_id, user_a_id

                    user_pair = (user_a_id, user_b_id)

                    # 直接筛选两个用户的记录 - 修复：使用digital_id而不是member_id
                    user_a_mask = group['digital_id'] == user_a_id
                    user_b_mask = group['digital_id'] == user_b_id

                    user_a_records = group[user_a_mask]
                    user_b_records = group[user_b_mask]

                    if len(user_a_records) > 0 and len(user_b_records) > 0:
                        # 计算匹配次数
                        additional_records = (len(user_a_records) - 1) + (len(user_b_records) - 1)
                        match_count = 1 + additional_records if additional_records > 0 else 1
                        user_pairs_count[user_pair] = match_count

            # 生成所有可能的配对（基于最新记录，确保每个用户对只处理一次）
            unique_records = list(latest_records.values())

            for i in range(len(unique_records)):
                for j in range(i+1, len(unique_records)):
                    if unique_records[i]['digital_id'] == unique_records[j]['digital_id']:  # 修复：使用digital_id
                        continue

                    # 获取用户对 - 修复：使用digital_id而不是member_id
                    user_a_id = unique_records[i]['digital_id']
                    user_b_id = unique_records[j]['digital_id']

                    # 确保用户ID有序
                    if user_a_id > user_b_id:
                        user_a_id, user_b_id = user_b_id, user_a_id
                        unique_records[i], unique_records[j] = unique_records[j], unique_records[i]

                    user_pair = (user_a_id, user_b_id)

                    # 🔧 修复：避免重复处理（移除了clear()操作）
                    if user_pair in processed_pairs:
                        continue

                    processed_pairs.add(user_pair)

                    # 检查是否属于同一BD团队
                    same_bd = unique_records[i]['bd_name'] == unique_records[j]['bd_name']

                    match_count = user_pairs_count.get(user_pair, 1)

                    # 构建结果记录 - 修复：添加member_id字段映射
                    relation = {
                        'user_a_mid': user_a_id,  # digital_id (8位数字，用于层级分析)
                        'user_a_member_id': unique_records[i].get('member_id', ''),  # member_id (长hash，用于合约匹配)
                        'user_a_name': unique_records[i].get('username', ''),
                        'user_a_bd': unique_records[i].get('bd_name', '未分配'),
                        'user_a_level': unique_records[i].get('agent_level', '未知级别'),
                        'user_b_mid': user_b_id,  # digital_id (8位数字，用于层级分析)
                        'user_b_member_id': unique_records[j].get('member_id', ''),  # member_id (长hash，用于合约匹配)
                        'user_b_name': unique_records[j].get('username', ''),
                        'user_b_bd': unique_records[j].get('bd_name', '未分配'),
                        'user_b_level': unique_records[j].get('agent_level', '未知级别'),
                        'user_a_time': unique_records[i]['created_time'].strftime('%Y-%m-%d %H:%M:%S'),
                        'user_b_time': unique_records[j]['created_time'].strftime('%Y-%m-%d %H:%M:%S'),
                        'shared_type': '共享设备' if shared_field == 'device_id' else '共享IP',
                        'shared_value': name,  # 保持兼容
                        'same_bd': same_bd,
                        'match_count': match_count
                    }

                    # 🎯 根据字段类型添加对应的字段名，让存储逻辑更清晰
                    if shared_field == 'device_id':
                        relation['shared_device_id'] = name
                    elif shared_field == 'ip':
                        relation['shared_ip'] = name
                    result.append(relation)

        return result

    def _deduplicate_relationships(self, relationships):
        """
        对关系列表进行去重处理

        参数:
            relationships (list): 关系列表

        返回:
            list: 去重后的关系列表
        """
        if not relationships:
            return []

        # 使用集合来记录已处理的用户对+共享值组合
        seen_combinations = set()
        deduplicated = []

        for rel in relationships:
            # 创建唯一标识：用户对+共享值
            user_a_mid = rel.get('user_a_mid', '')
            user_b_mid = rel.get('user_b_mid', '')
            shared_value = rel.get('shared_value', '')

            # 确保用户ID有序
            if user_a_mid > user_b_mid:
                user_a_mid, user_b_mid = user_b_mid, user_a_mid

            # 创建唯一键
            unique_key = (user_a_mid, user_b_mid, shared_value)

            # 如果没有见过这个组合，则添加到结果中
            if unique_key not in seen_combinations:
                seen_combinations.add(unique_key)
                deduplicated.append(rel)

        logger.debug(f"去重前: {len(relationships)}条记录，去重后: {len(deduplicated)}条记录")
        return deduplicated

    def _find_both_shared_chunk(self, ip_chunk, device_pairs):
        """处理IP共享数据块，查找同时共享设备和IP的关系"""
        result = []
        processed_pairs = set()
        
        for ip_item in ip_chunk:
            user_a_mid = ip_item['user_a_mid']
            user_b_mid = ip_item['user_b_mid']
            
            # 确保用户ID有序
            user_pair = tuple(sorted([user_a_mid, user_b_mid]))
            
            # 如果该用户对已经处理过，则跳过
            if user_pair in processed_pairs:
                continue
                
            if user_pair in device_pairs:
                # 找到同时共享设备和IP的用户对
                device_item = device_pairs[user_pair][0]
                
                # 两者都共享就是一个更强的关联，匹配次数取共享设备和共享IP中的较大值
                match_count_device = device_item.get('match_count', 1)
                match_count_ip = ip_item.get('match_count', 1)
                match_count = max(match_count_device, match_count_ip)
                
                # 确保层级信息是字符串并且不为None，但保持空值让前端fallback逻辑生效
                user_a_level = ip_item.get('user_a_level', '')
                if user_a_level is None or pd.isna(user_a_level):
                    user_a_level = ''
                else:
                    user_a_level = str(user_a_level).strip()
                
                user_b_level = ip_item.get('user_b_level', '')
                if user_b_level is None or pd.isna(user_b_level):
                    user_b_level = ''
                else:
                    user_b_level = str(user_b_level).strip()
                
                # 构建结果
                result.append({
                    'user_a_mid': ip_item['user_a_mid'],
                    'user_a_name': ip_item['user_a_name'],
                    'user_a_bd': ip_item['user_a_bd'],
                    'user_a_level': user_a_level,
                    'user_b_mid': ip_item['user_b_mid'],
                    'user_b_name': ip_item['user_b_name'],
                    'user_b_bd': ip_item['user_b_bd'],
                    'user_b_level': user_b_level,
                    'user_a_time': ip_item['user_a_time'],
                    'user_b_time': ip_item['user_b_time'],
                    'shared_type': '两者都共享',
                    'shared_device_id': device_item['shared_device_id'],
                    'shared_ip': ip_item['shared_ip'],
                    'same_bd': ip_item['same_bd'],
                    'match_count': match_count
                })
                
                # 标记该用户对已处理
                processed_pairs.add(user_pair)
        
        return result
    
    def _filter_data_chunk(self, data_chunk, search_term, filter_bd, filter_unassigned_bd, filter_ourbit_internal, filter_mode='all'):
        """过滤数据块"""
        result = []
        
        for item in data_chunk:
            # 过滤未分配BD的数据
            if filter_unassigned_bd and (not item.get('user_a_bd') or item.get('user_a_bd') == '未分配' or 
                                         not item.get('user_b_bd') or item.get('user_b_bd') == '未分配'):
                continue
            
            # 过滤Ourbit_Internal的数据
            if filter_ourbit_internal and (item.get('user_a_bd') == 'Ourbit_Internal' or item.get('user_b_bd') == 'Ourbit_Internal'):
                continue
            
            # BD团队过滤 - 根据筛选模式进行不同的处理
            if filter_bd:
                user_a_bd = item.get('user_a_bd')
                user_b_bd = item.get('user_b_bd')
                
                if filter_bd == '未分配':
                    # 处理未分配BD的情况
                    if filter_mode == 'same_bd':
                        # 同一BD模式：双方都是未分配
                        if not (
                            (not user_a_bd or user_a_bd == '未分配') and 
                            (not user_b_bd or user_b_bd == '未分配')
                        ):
                            continue
                    elif filter_mode == 'diff_bd':
                        # 不同BD模式：至少有一方是未分配，但不能双方都是未分配
                        is_a_unassigned = (not user_a_bd or user_a_bd == '未分配')
                        is_b_unassigned = (not user_b_bd or user_b_bd == '未分配')
                        if not ((is_a_unassigned or is_b_unassigned) and not (is_a_unassigned and is_b_unassigned)):
                            continue
                    else:
                        # 全部模式：至少有一方是未分配
                        if not ((not user_a_bd or user_a_bd == '未分配') or (not user_b_bd or user_b_bd == '未分配')):
                            continue
                else:
                    # 处理具体BD团队的情况
                    if filter_mode == 'same_bd':
                        # 同一BD模式：双方都属于指定BD团队
                        if not (user_a_bd == filter_bd and user_b_bd == filter_bd):
                            continue
                    elif filter_mode == 'diff_bd':
                        # 不同BD模式：至少有一方属于指定BD团队，但不能双方都是同一BD团队
                        has_target_bd = (user_a_bd == filter_bd or user_b_bd == filter_bd)
                        both_same_bd = (user_a_bd == user_b_bd and user_a_bd == filter_bd)
                        if not (has_target_bd and not both_same_bd):
                            continue
                    else:
                        # 全部模式：至少有一方属于指定BD团队
                        if not (user_a_bd == filter_bd or user_b_bd == filter_bd):
                            continue
            
            # 搜索过滤
            if search_term:
                search_fields = [
                    # 用户ID
                    str(item.get('user_a_mid') or ''),
                    str(item.get('user_b_mid') or ''),
                    # 用户名称
                    str(item.get('user_a_name') or ''),
                    str(item.get('user_b_name') or ''),
                    # BD团队
                    str(item.get('user_a_bd') or ''),
                    str(item.get('user_b_bd') or ''),
                    # 用户层级
                    str(item.get('user_a_level') or ''),
                    str(item.get('user_b_level') or ''),
                    # 共享设备ID
                    str(item.get('shared_device_id') or item.get('shared_value') or ''),
                    # 共享IP
                    str(item.get('shared_ip') or (item.get('shared_value') if item.get('shared_type') == '共享IP' else '') or '')
                ]
                
                if not any(search_term in field.lower() for field in search_fields):
                    continue
            
            result.append(item)
        
        return result
    
    def _analyze_bd_statistics_for_type(self, type_name, shared_data):
        """分析特定类型共享数据的BD团队统计"""
        bd_stats = {}
        
        if not shared_data:
            return []
        
        for item in shared_data:
            bd_name = item.get('user_a_bd', '')
            if not bd_name:
                bd_name = '未分配'
                
            # 获取唯一用户对
            user_pair = tuple(sorted([item.get('user_a_mid', ''), item.get('user_b_mid', '')]))
            
            if bd_name not in bd_stats:
                bd_stats[bd_name] = set()
            
            bd_stats[bd_name].add(user_pair)
        
        # 转换为排序后的列表结果
        result = []
        for bd_name, user_pairs in bd_stats.items():
            result.append({
                'bd_name': bd_name,
                'count': len(user_pairs)
            })
        
        return sorted(result, key=lambda x: x['count'], reverse=True) 