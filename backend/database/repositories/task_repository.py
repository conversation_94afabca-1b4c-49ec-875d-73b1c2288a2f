"""
任务数据仓库
处理任务管理和分析结果的数据库操作
"""
from typing import List, Dict, Optional, Any
from ..duckdb_manager import db_manager
import json
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class TaskRepository:
    """任务数据仓库"""
    
    def create_task(self, task_id: str, task_type: str, filename: Optional[str] = None, initial_status: str = 'pending') -> None:
        """创建新任务 - 修复：使用简单INSERT避免DuckDB的INSERT OR REPLACE问题"""
        try:
            # 先检查任务是否已存在
            existing_task = self.get_task(task_id)
            if existing_task:
                logger.warning(f"任务 {task_id} 已存在，跳过创建")
                return

            # 使用简单的INSERT，避免INSERT OR REPLACE的问题
            # 支持设置初始状态，避免创建后立即更新的竞态条件
            initial_message = '任务已创建' if initial_status == 'pending' else f'任务已创建并开始{initial_status}'
            sql = """
            INSERT INTO tasks (task_id, task_type, status, filename, progress, message, created_at, updated_at)
            VALUES (?, ?, ?, ?, 0, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """
            db_manager.execute_sql_no_return(sql, [task_id, task_type, initial_status, filename, initial_message])

            # 验证任务是否真的创建成功
            verify_task = self.get_task(task_id)
            if verify_task:
                logger.info(f"✅ 创建任务成功: {task_id} ({task_type})")
            else:
                raise Exception(f"任务创建后验证失败: {task_id}")

        except Exception as e:
            logger.error(f"❌ 创建任务失败: {task_id} ({task_type}), 错误: {e}")
            raise
    
    def update_task_status(self, task_id: str, status: str, progress: int = None, message: str = None) -> None:
        """更新任务状态 - 修复：使用简单UPDATE避免DuckDB的INSERT OR REPLACE问题"""
        try:
            logger.info(f"开始更新任务状态: {task_id} -> {status}")

            # 检查任务是否存在
            existing_task = self.get_task(task_id)
            if not existing_task:
                logger.error(f"任务 {task_id} 不存在，无法更新状态")
                return

            # 使用简单的UPDATE语句，避免复杂的事务操作
            if status == 'completed':
                sql = """
                UPDATE tasks
                SET status = ?,
                    progress = COALESCE(?, progress),
                    message = COALESCE(?, message),
                    updated_at = CURRENT_TIMESTAMP,
                    completed_at = CURRENT_TIMESTAMP
                WHERE task_id = ?
                """
            else:
                sql = """
                UPDATE tasks
                SET status = ?,
                    progress = COALESCE(?, progress),
                    message = COALESCE(?, message),
                    updated_at = CURRENT_TIMESTAMP
                WHERE task_id = ?
                """

            db_manager.execute_sql_no_return(sql, [status, progress, message, task_id])
            logger.info(f"✅ 任务状态更新成功: {task_id} -> {status}")

        except Exception as e:
            logger.error(f"❌ 任务状态更新失败 - task_id: {task_id}, status: {status}, 错误: {str(e)}")
            # 不抛出异常，避免影响主流程
            logger.warning(f"⚠️ 任务状态更新失败，但继续执行: {task_id} -> {status}")

    def update_task_result_summary(self, task_id: str, result_summary: dict) -> None:
        """更新任务结果摘要 - 使用UPDATE语句避免主键冲突"""
        try:
            # 使用事务确保操作的原子性
            with db_manager.get_connection() as conn:
                # 开始事务
                conn.execute("BEGIN TRANSACTION")

                try:
                    # 检查任务是否存在
                    check_result = conn.execute("SELECT COUNT(*) FROM tasks WHERE task_id = ?", [task_id])
                    task_exists = check_result.fetchone()[0] > 0

                    if task_exists:
                        # 使用UPDATE语句更新结果摘要
                        sql = """
                        UPDATE tasks
                        SET result_summary = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE task_id = ?
                        """
                        params = [
                            json.dumps(result_summary, ensure_ascii=False) if result_summary else None,
                            task_id
                        ]

                        conn.execute(sql, params)

                        # 提交事务
                        conn.execute("COMMIT")
                        logger.info(f"✅ 更新任务结果摘要成功: {task_id}")
                    else:
                        # 回滚事务
                        conn.execute("ROLLBACK")
                        logger.warning(f"任务 {task_id} 不存在，无法更新结果摘要")

                except Exception as tx_error:
                    # 回滚事务
                    try:
                        conn.execute("ROLLBACK")
                        logger.warning(f"事务已回滚: {task_id}")
                    except:
                        pass
                    raise tx_error

        except Exception as e:
            logger.error(f"更新任务结果摘要失败: {str(e)}")
            raise
    
    def update_task_result(self, task_id: str, result_summary: Dict[str, Any]) -> None:
        """更新任务结果摘要"""
        try:
            # 基本安全检查：防止写入过大的数据
            json_str = json.dumps(result_summary, ensure_ascii=False)
            json_size_mb = len(json_str.encode('utf-8')) / (1024 * 1024)
            
            # 如果数据超过50MB，直接报错
            if json_size_mb > 50:
                error_msg = f"结果数据过大 ({json_size_mb:.1f}MB)，无法保存。请检查数据处理逻辑。"
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            # 如果数据在10-50MB之间，记录警告但继续处理
            if json_size_mb > 10:
                logger.warning(f"结果数据较大 ({json_size_mb:.1f}MB)，建议优化存储方式")
            
            # 执行数据库更新
            sql = """
            UPDATE tasks 
            SET result_summary = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE task_id = ?
            """
            db_manager.execute_sql(sql, [json_str, task_id])
            
            logger.info(f"任务 {task_id} 结果已保存 (大小: {json_size_mb:.2f}MB)")
            
        except Exception as e:
            logger.error(f"更新任务结果失败 - task_id: {task_id}, 错误: {str(e)}")
            raise  # 直接抛出异常，让调用方处理
    
    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务详情"""
        sql = "SELECT * FROM tasks WHERE task_id = ?"
        result = db_manager.execute_sql(sql, [task_id])
        if result:
            task = result[0]
            # 解析JSON字段
            if task.get('result_summary'):
                try:
                    task['result_summary'] = json.loads(task['result_summary'])
                except:
                    task['result_summary'] = {}
            return task
        return None
    
    def get_tasks_by_type(self, task_type: str, limit: int = 50) -> List[Dict[str, Any]]:
        """获取指定类型的任务列表"""
        # 限制最大查询数量，避免内存问题
        safe_limit = min(limit, 20)
        sql = """
        SELECT task_id, task_type, status, filename, progress, message, created_at, updated_at, completed_at
        FROM tasks 
        WHERE task_type = ? 
        ORDER BY created_at DESC 
        LIMIT ?
        """
        results = db_manager.execute_sql(sql, [task_type, safe_limit])
        
        # 解析JSON字段
        for task in results:
            if task.get('result_summary'):
                try:
                    task['result_summary'] = json.loads(task['result_summary'])
                except:
                    task['result_summary'] = {}
        
        return results
    
    def get_recent_tasks(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取最近的任务"""
        # 限制最大查询数量，避免内存问题
        safe_limit = min(limit, 10)
        sql = """
        SELECT task_id, task_type, status, filename, progress, message, created_at, updated_at, completed_at
        FROM tasks 
        ORDER BY created_at DESC 
        LIMIT ?
        """
        results = db_manager.execute_sql(sql, [safe_limit])
        
        # 解析JSON字段
        for task in results:
            if task.get('result_summary'):
                try:
                    task['result_summary'] = json.loads(task['result_summary'])
                except:
                    task['result_summary'] = {}
        
        return results
    
    def get_all_tasks(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取所有任务"""
        # 限制最大查询数量，避免内存问题
        safe_limit = min(limit, 100)
        sql = """
        SELECT task_id, task_type as type, status, filename, progress, message, created_at, updated_at, completed_at,
               task_id as name, 
               COALESCE(filename, '未知文件') as description,
               0 as file_count,
               0 as data_count
        FROM tasks 
        ORDER BY created_at DESC 
        LIMIT ?
        """
        results = db_manager.execute_sql(sql, [safe_limit])
        
        # 解析JSON字段并格式化数据
        for task in results:
            if task.get('result_summary'):
                try:
                    task['result_summary'] = json.loads(task['result_summary'])
                except:
                    task['result_summary'] = {}
            
            # 格式化任务名称
            if not task.get('name') or task['name'] == task.get('task_id'):
                task_type = task.get('type', 'unknown')
                filename = task.get('filename', '')
                if filename:
                    task['name'] = f"{task_type}_{filename}"
                else:
                    task['name'] = f"{task_type}_{task.get('task_id', '')[:8]}"
        
        return results
    
    def get_pending_tasks(self) -> List[Dict[str, Any]]:
        """获取待处理的任务"""
        sql = """
        SELECT * FROM tasks 
        WHERE status IN ('pending', 'processing') 
        ORDER BY created_at ASC
        """
        return db_manager.execute_sql(sql)
    
    def delete_task(self, task_id: str) -> None:
        """删除任务"""
        sql = "DELETE FROM tasks WHERE task_id = ?"
        db_manager.execute_sql(sql, [task_id])
        logger.info(f"删除任务: {task_id}")
    
    def cleanup_old_tasks(self, days: int = 30) -> int:
        """清理旧任务"""
        # DuckDB使用INTERVAL语法
        sql = """
        DELETE FROM tasks 
        WHERE created_at < (CURRENT_TIMESTAMP - INTERVAL '{} days')
        AND status = 'completed'
        """.format(days)
        
        # 先查询要删除的任务数量
        count_sql = """
        SELECT COUNT(*) as count FROM tasks 
        WHERE created_at < (CURRENT_TIMESTAMP - INTERVAL '{} days')
        AND status = 'completed'
        """.format(days)
        
        count_result = db_manager.execute_sql(count_sql)
        cleaned_count = count_result[0]['count'] if count_result else 0
        
        # 执行删除
        if cleaned_count > 0:
            db_manager.execute_sql(sql)
        
        logger.info(f"清理了{cleaned_count}个旧任务")
        return cleaned_count
    
    def get_completed_tasks(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取已完成的任务列表"""
        sql = """
        SELECT * FROM tasks 
        WHERE status = 'completed'
        ORDER BY completed_at DESC 
        LIMIT ?
        """
        results = db_manager.execute_sql(sql, [limit])
        
        # 解析JSON字段
        for task in results:
            if task.get('result_summary'):
                try:
                    task['result_summary'] = json.loads(task['result_summary'])
                except:
                    task['result_summary'] = {}
        
        return results
    
    def get_task_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        sql = """
        SELECT 
            task_type,
            status,
            COUNT(*) as count
        FROM tasks 
        GROUP BY task_type, status
        ORDER BY task_type, status
        """
        
        stats_result = db_manager.execute_sql(sql)
        
        # 重新组织数据结构
        stats = {}
        for row in stats_result:
            task_type = row['task_type']
            if task_type not in stats:
                stats[task_type] = {}
            stats[task_type][row['status']] = row['count']
        
        return stats

    def batch_insert_shared_relationships(self, task_id: str, relationships: List[Dict[str, Any]]) -> None:
        """批量插入共享关系数据"""
        if not relationships:
            return

        # 🔧 修复：在插入前进行去重处理
        deduplicated_relationships = self._deduplicate_relationships_for_insert(task_id, relationships)

        if not deduplicated_relationships:
            logger.info(f"任务 {task_id} 的关系数据去重后为空，跳过插入")
            return

        logger.info(f"任务 {task_id} 关系数据去重：原始 {len(relationships)} 条，去重后 {len(deduplicated_relationships)} 条")

        sql = """
        INSERT INTO shared_relationships (
            task_id, relationship_type, shared_value,
            user_a_mid, user_a_member_id, user_a_name, user_a_bd, user_a_level, user_a_time,
            user_b_mid, user_b_member_id, user_b_name, user_b_bd, user_b_level, user_b_time,
            same_bd, match_count
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        params_list = []
        for rel in deduplicated_relationships:
            params_list.append([
                task_id,
                rel.get('relationship_type', ''),
                rel.get('shared_value', ''),
                rel.get('user_a_mid', ''),
                rel.get('user_a_member_id', ''),
                rel.get('user_a_name', ''),
                rel.get('user_a_bd', ''),
                rel.get('user_a_level', ''),
                rel.get('user_a_time', ''),
                rel.get('user_b_mid', ''),
                rel.get('user_b_member_id', ''),
                rel.get('user_b_name', ''),
                rel.get('user_b_bd', ''),
                rel.get('user_b_level', ''),
                rel.get('user_b_time', ''),
                rel.get('same_bd', False),
                rel.get('match_count', 1)
            ])

        db_manager.execute_many(sql, params_list)
        logger.info(f"批量插入{len(deduplicated_relationships)}条共享关系记录到任务{task_id}")

    def _deduplicate_relationships_for_insert(self, task_id: str, relationships: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        为插入操作进行关系去重处理

        参数:
            task_id (str): 任务ID
            relationships (List[Dict]): 关系列表

        返回:
            List[Dict]: 去重后的关系列表
        """
        if not relationships:
            return []

        # 使用集合来记录已处理的唯一组合
        seen_combinations = set()
        deduplicated = []

        for rel in relationships:
            # 创建唯一标识：任务ID + 关系类型 + 用户对 + 共享值
            user_a_mid = str(rel.get('user_a_mid', ''))
            user_b_mid = str(rel.get('user_b_mid', ''))
            shared_value = str(rel.get('shared_value', ''))
            relationship_type = str(rel.get('relationship_type', ''))

            # 确保用户ID有序
            if user_a_mid > user_b_mid:
                user_a_mid, user_b_mid = user_b_mid, user_a_mid

            # 创建唯一键
            unique_key = (task_id, relationship_type, user_a_mid, user_b_mid, shared_value)

            # 如果没有见过这个组合，则添加到结果中
            if unique_key not in seen_combinations:
                seen_combinations.add(unique_key)
                # 确保用户ID顺序正确
                if str(rel.get('user_a_mid', '')) > str(rel.get('user_b_mid', '')):
                    # 交换用户A和用户B的信息
                    rel_copy = rel.copy()
                    rel_copy['user_a_mid'] = rel.get('user_b_mid', '')
                    rel_copy['user_a_member_id'] = rel.get('user_b_member_id', '')
                    rel_copy['user_a_name'] = rel.get('user_b_name', '')
                    rel_copy['user_a_bd'] = rel.get('user_b_bd', '')
                    rel_copy['user_a_level'] = rel.get('user_b_level', '')
                    rel_copy['user_a_time'] = rel.get('user_b_time', '')
                    rel_copy['user_b_mid'] = rel.get('user_a_mid', '')
                    rel_copy['user_b_member_id'] = rel.get('user_a_member_id', '')
                    rel_copy['user_b_name'] = rel.get('user_a_name', '')
                    rel_copy['user_b_bd'] = rel.get('user_a_bd', '')
                    rel_copy['user_b_level'] = rel.get('user_a_level', '')
                    rel_copy['user_b_time'] = rel.get('user_a_time', '')
                    deduplicated.append(rel_copy)
                else:
                    deduplicated.append(rel)

        return deduplicated

    def get_shared_relationships(self, task_id: str, relationship_type: str = None,
                               page: int = 1, page_size: int = 1000,
                               bd_filter: str = '', search_term: str = '') -> Dict[str, Any]:
        """分页获取共享关系数据"""
        offset = (page - 1) * page_size
        
        # 构建基础查询
        base_sql = "SELECT * FROM shared_relationships WHERE task_id = ?"
        count_sql = "SELECT COUNT(*) as total FROM shared_relationships WHERE task_id = ?"
        params = [task_id]
        
        # 添加关系类型过滤
        if relationship_type:
            base_sql += " AND relationship_type = ?"
            count_sql += " AND relationship_type = ?"
            params.append(relationship_type)
        
        # 添加BD过滤
        if bd_filter:
            base_sql += " AND (user_a_bd = ? OR user_b_bd = ?)"
            count_sql += " AND (user_a_bd = ? OR user_b_bd = ?)"
            params.extend([bd_filter, bd_filter])
        
        # 添加搜索过滤
        if search_term:
            search_condition = " AND (user_a_name LIKE ? OR user_b_name LIKE ? OR user_a_mid LIKE ? OR user_b_mid LIKE ?)"
            search_param = f"%{search_term}%"
            base_sql += search_condition
            count_sql += search_condition
            params.extend([search_param, search_param, search_param, search_param])
        
        # 获取总数
        total_result = db_manager.execute_sql(count_sql, params)
        total = total_result[0]['total'] if total_result else 0
        
        # 获取分页数据
        base_sql += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
        params.extend([page_size, offset])
        
        relationships = db_manager.execute_sql(base_sql, params)
        
        return {
            'relationships': relationships,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total': total,
                'total_pages': (total + page_size - 1) // page_size
            }
        }
    
    def delete_shared_relationships(self, task_id: str) -> None:
        """删除指定任务的所有共享关系数据"""
        sql = "DELETE FROM shared_relationships WHERE task_id = ?"
        db_manager.execute_sql(sql, [task_id])
        logger.info(f"删除任务{task_id}的所有共享关系数据")
    
    def get_relationship_statistics(self, task_id: str, relationship_type: str = None) -> Dict[str, Any]:
        """获取指定任务的关系统计信息"""
        try:
            if relationship_type:
                sql = """
                SELECT 
                    COUNT(*) as total_count,
                    COUNT(DISTINCT user_a_mid) as unique_users_a,
                    COUNT(DISTINCT user_b_mid) as unique_users_b,
                    COUNT(DISTINCT shared_value) as unique_shared_values
                FROM shared_relationships 
                WHERE task_id = ? AND relationship_type = ?
                """
                result = db_manager.execute_sql(sql, [task_id, relationship_type])
            else:
                sql = """
                SELECT 
                    relationship_type,
                    COUNT(*) as total_count,
                    COUNT(DISTINCT user_a_mid) as unique_users_a,
                    COUNT(DISTINCT user_b_mid) as unique_users_b,
                    COUNT(DISTINCT shared_value) as unique_shared_values
                FROM shared_relationships 
                WHERE task_id = ?
                GROUP BY relationship_type
                """
                result = db_manager.execute_sql(sql, [task_id])
            
            if result:
                if relationship_type:
                    # 单个类型的统计
                    stats = result[0]
                    return {
                        'total_count': stats.get('total_count', 0),
                        'unique_users_a': stats.get('unique_users_a', 0),
                        'unique_users_b': stats.get('unique_users_b', 0),
                        'unique_shared_values': stats.get('unique_shared_values', 0)
                    }
                else:
                    # 所有类型的统计
                    stats_by_type = {}
                    for row in result:
                        rel_type = row.get('relationship_type', 'unknown')
                        stats_by_type[rel_type] = {
                            'total_count': row.get('total_count', 0),
                            'unique_users_a': row.get('unique_users_a', 0),
                            'unique_users_b': row.get('unique_users_b', 0),
                            'unique_shared_values': row.get('unique_shared_values', 0)
                        }
                    return stats_by_type
            else:
                # 没有数据时返回默认值
                if relationship_type:
                    return {
                        'total_count': 0,
                        'unique_users_a': 0,
                        'unique_users_b': 0,
                        'unique_shared_values': 0
                    }
                else:
                    return {}
                    
        except Exception as e:
            logger.error(f"获取关系统计失败 - task_id: {task_id}, type: {relationship_type}, 错误: {str(e)}")
            # 返回默认值而不是抛出异常
            if relationship_type:
                return {
                    'total_count': 0,
                    'unique_users_a': 0,
                    'unique_users_b': 0,
                    'unique_shared_values': 0
                }
            else:
                return {}

class ContractRiskRepository:
    """合约风险分析数据仓库 - 已废弃，保留接口兼容性"""

    def save_analysis_result(self, task_id: str, analysis_type: str, filename: str,
                           total_contracts: int, risk_contracts: int,
                           wash_trading_count: int, cross_bd_count: int,
                           result_data: Dict[str, Any]) -> None:
        """保存分析结果 - 已废弃，数据现在保存到新存储系统"""
        # 注意：contract_risk_analysis 表已被移除
        # 数据现在统一保存到新的存储系统（algorithm_results等表）
        logger.info(f"保存分析结果（已废弃）: {task_id}")
        pass

    def get_analysis_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取分析结果 - 已废弃，从新存储系统获取"""
        # 注意：contract_risk_analysis 表已被移除
        # 应该使用 ContractRiskRepository 的新方法或直接使用新存储系统
        return None
    


# 创建全局仓库实例
task_repository = TaskRepository()
contract_risk_repository = ContractRiskRepository() 